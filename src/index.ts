import { Hono } from 'hono'
import { cors } from 'hono/cors'

type Bindings = {
  ENVIRONMENT: string
}

const app = new Hono<{ Bindings: Bindings }>()

app.use('*', cors())

// API 路由 - 生成图片
app.post('/api/generate', async (c) => {
  try {
    const { prompt, guidance_scale, num_images, aspect_ratio, output_format, safety_tolerance, api_key } = await c.req.json()
    
    if (!api_key) {
      return c.json({ error: 'API Key 是必需的' }, 400)
    }

    if (!prompt) {
      return c.json({ error: '描述不能为空' }, 400)
    }

    // 调用 FAL AI API
    const response = await fetch('https://fal.run/fal-ai/flux-pro/kontext/text-to-image', {
      method: 'POST',
      headers: {
        'Authorization': `Key ${api_key}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        guidance_scale: parseFloat(guidance_scale) || 3.5,
        num_images: parseInt(num_images) || 1,
        aspect_ratio: aspect_ratio || '1:1',
        output_format: output_format || 'jpeg',
        safety_tolerance: safety_tolerance || '2'
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      return c.json({ error: '图片生成失败', details: errorData }, response.status)
    }

    const result = await response.json()
    return c.json(result)
  } catch (error) {
    console.error('生成图片时出错:', error)
    return c.json({ error: '服务器内部错误' }, 500)
  }
})

// 静态文件服务
app.get('/', (c) => {
  return c.html(getIndexHTML())
})

// 获取主页 HTML
function getIndexHTML() {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟AI图像生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            gap: 20px;
            min-height: 100vh;
        }
        
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .settings-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .settings-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .main-content {
            margin-top: 80px;
            display: flex;
            gap: 20px;
            width: 100%;
        }
        
        .left-panel {
            flex: 1;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .right-panel {
            flex: 1;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 120px;
            transition: border-color 0.3s ease;
        }
        
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .generate-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }
        
        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-area {
            text-align: center;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .result-image {
            max-width: 100%;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .result-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .download-btn, .regenerate-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
                 .download-btn {
             background: #10b981;
             color: white;
         }
         
         .download-btn:disabled {
             background: #9ca3af;
             cursor: not-allowed;
             transform: none;
         }
         
         .regenerate-btn {
             background: #6b7280;
             color: white;
         }
        
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 12px;
            width: 400px;
            max-width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .close {
            font-size: 28px;
            cursor: pointer;
            color: #999;
        }
        
        .close:hover {
            color: #333;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .placeholder-text {
            color: #999;
            font-size: 16px;
        }
        
        .error-message {
            color: #dc2626;
            background: #fef2f2;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            border-left: 4px solid #dc2626;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🌟AI图像生成器</div>
        <button class="settings-btn" onclick="openSettings()">⚙️ 设置</button>
    </div>
    
    <div class="container">
        <div class="main-content">
            <div class="left-panel">
                <div class="section-title">描述你想要的图像：</div>
                <form id="generateForm">
                    <div class="form-group">
                        <textarea 
                            id="prompt" 
                            placeholder="例如：Wearing a skirt made of raindrops 🌧️"
                            required
                        ></textarea>
                    </div>
                    
                    <div class="section-title">生成参数</div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>宽高比：</label>
                            <select id="aspect_ratio">
                                <option value="16:9">16:9 (宽屏)</option>
                                <option value="1:1" selected>1:1 (方形)</option>
                                <option value="9:16">9:16 (竖屏)</option>
                                <option value="4:3">4:3</option>
                                <option value="3:2">3:2</option>
                                <option value="2:3">2:3</option>
                                <option value="3:4">3:4</option>
                                <option value="21:9">21:9</option>
                                <option value="9:21">9:21</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>生成数量：</label>
                            <select id="num_images">
                                <option value="1" selected>1张</option>
                                <option value="2">2张</option>
                                <option value="3">3张</option>
                                <option value="4">4张</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>创意度 (1-20)：</label>
                            <input type="number" id="guidance_scale" value="3.5" min="1" max="20" step="0.5">
                        </div>
                        
                        <div class="form-group">
                            <label>输出格式：</label>
                            <select id="output_format">
                                <option value="jpeg" selected>JPEG</option>
                                <option value="png">PNG</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>安全等级：</label>
                        <select id="safety_tolerance">
                            <option value="1">1 (最严格)</option>
                            <option value="2" selected>2 (标准)</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5 (最宽松)</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="generate-btn" id="generateBtn">
                        ✨ 生成图像
                    </button>
                </form>
            </div>
            
            <div class="right-panel">
                <div class="section-title">生成结果</div>
                <div class="result-area" id="resultArea">
                    <div class="placeholder-text">
                        <p>🎨 在左侧输入描述，开始创作你的 AI 艺术作品</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 设置模态框 -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>API 设置</h2>
                <span class="close" onclick="closeSettings()">&times;</span>
            </div>
            <div class="form-group">
                <label>FAL AI API Key：</label>
                <input type="password" id="apiKey" placeholder="输入你的 API Key">
                <small style="color: #666; display: block; margin-top: 5px;">
                    你的 API Key 将安全存储在本地浏览器中
                </small>
            </div>
            <button onclick="saveSettings()" style="width: 100%; padding: 12px; background: #667eea; color: white; border: none; border-radius: 6px; font-weight: 600; margin-top: 15px;">
                保存设置
            </button>
        </div>
    </div>
    
    <script>
        // 全局变量
        let currentResults = null;
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadApiKey();
        });
        
        // 设置相关函数
        function openSettings() {
            document.getElementById('settingsModal').style.display = 'block';
        }
        
        function closeSettings() {
            document.getElementById('settingsModal').style.display = 'none';
        }
        
        function saveSettings() {
            const apiKey = document.getElementById('apiKey').value.trim();
            if (apiKey) {
                localStorage.setItem('fal_api_key', apiKey);
                alert('API Key 已保存！');
                closeSettings();
            } else {
                alert('请输入有效的 API Key');
            }
        }
        
        function loadApiKey() {
            const savedKey = localStorage.getItem('fal_api_key');
            if (savedKey) {
                document.getElementById('apiKey').value = savedKey;
            }
        }
        
        // 表单提交处理
        document.getElementById('generateForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiKey = localStorage.getItem('fal_api_key');
            if (!apiKey) {
                alert('请先设置 API Key！');
                openSettings();
                return;
            }
            
            const formData = {
                prompt: document.getElementById('prompt').value.trim(),
                guidance_scale: document.getElementById('guidance_scale').value,
                num_images: document.getElementById('num_images').value,
                aspect_ratio: document.getElementById('aspect_ratio').value,
                output_format: document.getElementById('output_format').value,
                safety_tolerance: document.getElementById('safety_tolerance').value,
                api_key: apiKey
            };
            
            if (!formData.prompt) {
                alert('请输入图像描述！');
                return;
            }
            
            await generateImage(formData);
        });
        
        // 生成图像函数
        async function generateImage(formData) {
            const generateBtn = document.getElementById('generateBtn');
            const resultArea = document.getElementById('resultArea');
            
            // 显示加载状态
            generateBtn.disabled = true;
            generateBtn.textContent = '生成中...';
            
            resultArea.innerHTML = \`
                <div class="loading">
                    <div class="spinner"></div>
                    <p>AI 正在创作中，请稍候...</p>
                </div>
            \`;
            
            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.error || '生成失败');
                }
                
                currentResults = result;
                displayResults(result, formData.prompt);
                
            } catch (error) {
                console.error('生成错误:', error);
                resultArea.innerHTML = \`
                    <div class="error-message">
                        <p><strong>生成失败：</strong>\${error.message}</p>
                        <p>请检查你的 API Key 或稍后重试。</p>
                    </div>
                \`;
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '✨ 生成图像';
            }
        }
        
        // 显示结果
        function displayResults(result, prompt) {
            const resultArea = document.getElementById('resultArea');
            
            if (result.images && result.images.length > 0) {
                const imagesHtml = result.images.map((image, index) => \`
                    <div style="margin-bottom: 20px;">
                        <img src="\${image.url}" alt="Generated Image \${index + 1}" class="result-image">
                    </div>
                \`).join('');
                
                resultArea.innerHTML = \`
                    <div>
                        \${imagesHtml}
                                                 <div class="result-actions">
                             <button class="download-btn" id="downloadBtn" onclick="downloadImages()">📥 下载图片</button>
                             <button class="regenerate-btn" onclick="regenerateImage()">🔄 重新生成</button>
                         </div>
                        <p style="margin-top: 15px; color: #666; font-size: 14px;">
                            提示词：\${prompt}
                        </p>
                    </div>
                \`;
            } else {
                resultArea.innerHTML = \`
                    <div class="error-message">
                        <p>未能生成图像，请重试。</p>
                    </div>
                \`;
            }
        }
        
                 // 下载图片
         async function downloadImages() {
             if (!currentResults || !currentResults.images) return;
             
             const downloadBtn = document.getElementById('downloadBtn');
             if (downloadBtn) {
                 downloadBtn.disabled = true;
                 downloadBtn.textContent = '下载中...';
             }
             
             try {
                 for (let i = 0; i < currentResults.images.length; i++) {
                     const image = currentResults.images[i];
                     try {
                         // 获取图片数据
                         const response = await fetch(image.url);
                         if (!response.ok) throw new Error('网络请求失败');
                         
                         const blob = await response.blob();
                         
                         // 创建下载链接
                         const blobUrl = URL.createObjectURL(blob);
                         const link = document.createElement('a');
                         link.href = blobUrl;
                         
                         // 设置文件名
                         const extension = image.content_type.split('/')[1] || 'jpg';
                         const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                         link.download = \`ai-generated-\${timestamp}-\${i + 1}.\${extension}\`;
                         
                         // 设置下载属性，强制下载
                         link.setAttribute('download', link.download);
                         
                         // 触发下载
                         document.body.appendChild(link);
                         link.click();
                         document.body.removeChild(link);
                         
                         // 释放 blob URL
                         setTimeout(() => URL.revokeObjectURL(blobUrl), 1000);
                         
                         // 添加小延迟避免浏览器阻止多次下载
                         if (i < currentResults.images.length - 1) {
                             await new Promise(resolve => setTimeout(resolve, 500));
                         }
                     } catch (error) {
                         console.error(\`下载第 \${i + 1} 张图片失败:\`, error);
                         // 如果 fetch 失败，回退到直接打开链接
                         window.open(image.url, '_blank');
                     }
                 }
                 
                 // 显示成功提示
                 if (downloadBtn) {
                     downloadBtn.textContent = '✅ 下载完成';
                     setTimeout(() => {
                         downloadBtn.textContent = '📥 下载图片';
                         downloadBtn.disabled = false;
                     }, 2000);
                 }
                 
             } catch (error) {
                 console.error('下载过程出错:', error);
                 if (downloadBtn) {
                     downloadBtn.textContent = '❌ 下载失败';
                     setTimeout(() => {
                         downloadBtn.textContent = '📥 下载图片';
                         downloadBtn.disabled = false;
                     }, 2000);
                 }
             }
         }
        
        // 重新生成
        function regenerateImage() {
            document.getElementById('generateForm').dispatchEvent(new Event('submit'));
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('settingsModal');
            if (event.target === modal) {
                closeSettings();
            }
        }
    </script>
</body>
</html>`
}

export default app