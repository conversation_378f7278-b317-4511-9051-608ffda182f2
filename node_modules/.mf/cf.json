{"clientTcpRtt": 1, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "NA", "asn": 36352, "clientAcceptEncoding": "br, gzip, deflate", "country": "US", "isEUCountry": false, "verifiedBotCategory": "", "tlsVersion": "TLSv1.3", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientCiphersSha1": "kXrN3VEKDdzz2cPKTQaKzpxVTxQ=", "tlsExportedAuthenticator": {"clientFinished": "3b2b7a7c0c0baa085d041db495bfde7c9fcf4ee1d2969960a3f8f16d88399f440d7e17ff5d37bd1d0a0ac94f75977b71", "clientHandshake": "7cd7561c6ea59c90b8789a633cb2929acfa1ae553fb7af510d22fe650cadd4417948fa531e957ad025d598a61590f755", "serverHandshake": "ed74590a3225f8f1bb9ec63f0598c41a23287ccde740ae89cc26313b090209558c4d9a74d7f1bd3b7a0f000bfad54e99", "serverFinished": "72b0440053c9b6583239d50e79e57f82affdf025a51f717f15f0ffb61fa742bae32bddb1a38af74c879569a6eba0ab3e"}, "tlsClientHelloLength": "1605", "colo": "LAX", "timezone": "America/Los_Angeles", "region": "California", "longitude": "-118.24368", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "latitude": "34.05223", "postalCode": "90009", "city": "Los Angeles", "regionCode": "CA", "tlsClientRandom": "pHa6VC7LCxeJkAOlq/gvQFCiitQbGERKVii5y5cWfBg=", "tlsClientExtensionsSha1Le": "u4wtEMFQBY18l3BzHAvORm+KGRw=", "tlsClientExtensionsSha1": "1eY97BUYYO8vDaTfHQywB1pcNdM=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}